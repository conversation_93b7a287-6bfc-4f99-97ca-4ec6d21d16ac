#include "servo_ctrl.h"



uint8_t servos[15];
int8_t drive_height;

void servos_data_init()
{
	uint8_t i;
	for(i=0;i<12;i++)
	{
		servos[i+1]=90;
	}
  servos[2]=45;
	servos[4]=135;
	servos[6]=45;
	servos[8]=135;


	servos[0]=0x51;
	servos[13]=0;
	servos[14]=0x50;
}

void servos_data_init_lie()
{
	// 躺下模式：所有腿部舵机设置为收缩位置
	servos[1]=90;   // 左后大腿
	servos[2]=90;   // 左后小腿（收缩）
	servos[3]=90;   // 右后大腿
	servos[4]=90;   // 右后小腿（收缩）
	servos[5]=90;   // 左前大腿
	servos[6]=90;   // 左前小腿（收缩）
	servos[7]=90;   // 右前大腿
	servos[8]=90;   // 右前小腿（收缩）

  servos[0]=0x51;   // 包头
	servos[13]=0;     // 保留
	servos[14]=0x50;  // 包尾
}

void drive_mode()
{
	// 270度舵机适配：将180度角度映射到270度
	uint8_t thigh_angle = (uint8_t)(90 * 270.0/180.0);  // 90度映射到135度
	servos[1]=thigh_angle;
	servos[3]=thigh_angle;
	servos[5]=thigh_angle;
	servos[7]=thigh_angle;
	servos[2]=(uint8_t)((45-drive_height) * 270.0/180.0);
	servos[4]=(uint8_t)((135+drive_height) * 270.0/180.0);
	servos[6]=(uint8_t)((45-drive_height) * 270.0/180.0);
	servos[8]=(uint8_t)((135+drive_height) * 270.0/180.0);
}
