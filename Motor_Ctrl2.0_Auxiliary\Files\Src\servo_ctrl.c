#include "servo_ctrl.h"



uint8_t servos[15];
int8_t drive_height;

void servos_data_init()
{
	uint8_t i;
	// 270度舵机适配：中位是135度而不是90度
	for(i=0;i<12;i++)
	{
		servos[i+1]=135;  // 270度舵机的中位
	}
	// 270度舵机的小腿角度调整
  servos[2]=90;   // 左后小腿（45+45偏移）
	servos[4]=180;  // 右后小腿（135+45偏移）
	servos[6]=90;   // 左前小腿（45+45偏移）
	servos[8]=180;  // 右前小腿（135+45偏移）


	servos[0]=0x51;
	servos[13]=0;
	servos[14]=0x50;
}

void servos_data_init_lie()
{
	// 躺下模式：所有腿部舵机设置为收缩位置（270度舵机适配）
	servos[1]=135;   // 左后大腿（270度舵机中位）
	servos[2]=135;   // 左后小腿（收缩）
	servos[3]=135;   // 右后大腿
	servos[4]=135;   // 右后小腿（收缩）
	servos[5]=135;   // 左前大腿
	servos[6]=135;   // 左前小腿（收缩）
	servos[7]=135;   // 右前大腿
	servos[8]=135;   // 右前小腿（收缩）

  servos[0]=0x51;   // 包头
	servos[13]=0;     // 保留
	servos[14]=0x50;  // 包尾
}

void drive_mode()
{
	// 270度舵机适配：大腿中位是135度
	servos[1]=135;
	servos[3]=135;
	servos[5]=135;
	servos[7]=135;
	// 270度舵机适配：小腿角度调整
	servos[2]=90-drive_height;   // 左后小腿（45+45偏移）
	servos[4]=180+drive_height;  // 右后小腿（135+45偏移）
	servos[6]=90-drive_height;   // 左前小腿（45+45偏移）
	servos[8]=180+drive_height;  // 右前小腿（135+45偏移）
}
